<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Settings
{
    public static function init()
    {
        add_action('admin_menu', [self::class, 'add_pwa_settings_page']);
        add_action('admin_init', [self::class, 'register_pwa_settings']);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);
        add_action('wp_head', [self::class, 'add_pwa_meta_tags']);
        add_action('wp_head', [self::class, 'add_manifest_link']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueue_admin_scripts']);

        // Regenerate offline page when settings are updated
        add_action('update_option_q_pwa_offline_title', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_message', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_icon', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_show_cached_pages', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_show_tips', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_theme_color', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_app_name', [self::class, 'regenerate_offline_page']);
    }

    public static function add_pwa_settings_page()
    {
        add_submenu_page(
            'options-general.php',
            'PWA Settings',
            'PWA Settings',
            'manage_options',
            'q-pwa-settings',
            [self::class, 'render_pwa_settings_page']
        );
    }

    public static function register_pwa_settings()
    {
        // PWA Basic Settings
        register_setting('q_pwa_settings', 'q_pwa_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_short_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_description', [
            'type' => 'string',
            'default' => get_bloginfo('description'),
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_theme_color', [
            'type' => 'string',
            'default' => '#000000',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_background_color', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_display_mode', [
            'type' => 'string',
            'default' => 'standalone',
            'sanitize_callback' => [self::class, 'sanitize_display_mode']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_orientation', [
            'type' => 'string',
            'default' => 'any',
            'sanitize_callback' => [self::class, 'sanitize_orientation']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_start_url', [
            'type' => 'string',
            'default' => '/',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Offline Settings
        register_setting('q_pwa_settings', 'q_pwa_offline_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_cache_strategy', [
            'type' => 'string',
            'default' => 'cache_first',
            'sanitize_callback' => [self::class, 'sanitize_cache_strategy']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_page', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Offline Page Customization Settings
        register_setting('q_pwa_settings', 'q_pwa_offline_title', [
            'type' => 'string',
            'default' => 'You\'re Offline',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_message', [
            'type' => 'string',
            'default' => 'It looks like you\'ve lost your internet connection. Don\'t worry, you can still browse some cached content!',
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_icon', [
            'type' => 'string',
            'default' => '📡',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_show_cached_pages', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_show_tips', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Icon Settings
        register_setting('q_pwa_settings', 'q_pwa_icon_192', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_icon_512', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Add settings sections
        add_settings_section(
            'q_pwa_basic_section',
            'Basic PWA Settings',
            [self::class, 'render_basic_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_appearance_section',
            'Appearance Settings',
            [self::class, 'render_appearance_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_offline_section',
            'Offline & Caching Settings',
            [self::class, 'render_offline_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_icons_section',
            'App Icons',
            [self::class, 'render_icons_section_description'],
            'q_pwa_settings'
        );

        // Add settings fields
        self::add_settings_fields();
    }

    private static function add_settings_fields()
    {
        // Basic Settings Fields
        add_settings_field(
            'q_pwa_enabled',
            'Enable PWA',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']
        );

        add_settings_field(
            'q_pwa_app_name',
            'App Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters)']
        );

        add_settings_field(
            'q_pwa_app_short_name',
            'Short Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters)']
        );

        add_settings_field(
            'q_pwa_app_description',
            'Description',
            [self::class, 'render_textarea_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app']
        );

        add_settings_field(
            'q_pwa_start_url',
            'Start URL',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_start_url', 'description' => 'URL to load when app is launched (relative to site root)']
        );

        // Appearance Settings Fields
        add_settings_field(
            'q_pwa_theme_color',
            'Theme Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for the app']
        );

        add_settings_field(
            'q_pwa_background_color',
            'Background Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_background_color', 'description' => 'Background color for splash screen']
        );

        add_settings_field(
            'q_pwa_display_mode',
            'Display Mode',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_display_mode',
                'options' => [
                    'standalone' => 'Standalone (Recommended)',
                    'fullscreen' => 'Fullscreen',
                    'minimal-ui' => 'Minimal UI',
                    'browser' => 'Browser'
                ],
                'description' => 'How the app should be displayed when launched'
            ]
        );

        add_settings_field(
            'q_pwa_orientation',
            'Orientation',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_orientation',
                'options' => [
                    'any' => 'Any',
                    'portrait' => 'Portrait',
                    'landscape' => 'Landscape'
                ],
                'description' => 'Preferred screen orientation'
            ]
        );

        // Offline Settings Fields
        add_settings_field(
            'q_pwa_offline_enabled',
            'Enable Offline Support',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_enabled', 'description' => 'Cache content for offline access']
        );

        add_settings_field(
            'q_pwa_cache_strategy',
            'Cache Strategy',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            [
                'field' => 'q_pwa_cache_strategy',
                'options' => [
                    'cache_first' => 'Cache First (Faster)',
                    'network_first' => 'Network First (Fresh Content)',
                    'stale_while_revalidate' => 'Stale While Revalidate (Balanced)'
                ],
                'description' => 'How to handle caching for offline support'
            ]
        );

        // Icon Settings Fields
        add_settings_field(
            'q_pwa_icon_192',
            '192x192 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']
        );

        add_settings_field(
            'q_pwa_icon_512',
            '512x512 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']
        );
    }

    public static function render_pwa_settings_page()
    {
        require_once Q_PLUGIN_DIR . 'includes/templates/pwa-settings-page.php';
    }

    // Section descriptions
    public static function render_basic_section_description()
    {
        echo '<p>Configure basic Progressive Web App settings for your site.</p>';
    }

    public static function render_appearance_section_description()
    {
        echo '<p>Customize how your PWA looks and behaves when installed.</p>';
    }

    public static function render_offline_section_description()
    {
        echo '<p>Configure offline functionality and caching strategies.</p>';
    }

    public static function render_icons_section_description()
    {
        echo '<p>Upload icons for your PWA. Icons should be square and in PNG format.</p>';
    }

    // Field renderers
    public static function render_text_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="text" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_textarea_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<textarea name="' . esc_attr($field) . '" rows="3" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_checkbox_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, false);
        $description = $args['description'] ?? '';

        echo '<input type="checkbox" name="' . esc_attr($field) . '" value="1" ' . checked(1, $value, false) . ' />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_color_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '#000000');
        $description = $args['description'] ?? '';

        echo '<input type="color" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_select_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $options = $args['options'] ?? [];
        $description = $args['description'] ?? '';

        echo '<select name="' . esc_attr($field) . '">';
        foreach ($options as $option_value => $option_label) {
            echo '<option value="' . esc_attr($option_value) . '" ' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
        }
        echo '</select>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_media_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="url" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" class="button q-media-upload" data-field="' . esc_attr($field) . '">Upload Image</button>';
        if ($value) {
            echo '<br><img src="' . esc_url($value) . '" style="max-width: 100px; max-height: 100px; margin-top: 10px;" />';
        }
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    // Sanitization functions
    public static function sanitize_display_mode($value)
    {
        $allowed = ['standalone', 'fullscreen', 'minimal-ui', 'browser'];
        return in_array($value, $allowed) ? $value : 'standalone';
    }

    public static function sanitize_orientation($value)
    {
        $allowed = ['any', 'portrait', 'landscape'];
        return in_array($value, $allowed) ? $value : 'any';
    }

    public static function sanitize_cache_strategy($value)
    {
        $allowed = ['cache_first', 'network_first', 'stale_while_revalidate'];
        return in_array($value, $allowed) ? $value : 'cache_first';
    }

    // Frontend functionality
    public static function enqueue_pwa_scripts()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Enqueue PWA manager script
        wp_enqueue_script(
            'q-pwa-manager',
            Q_PLUGIN_URL . 'includes/js/pwa-manager.js',
            ['jquery'],
            '1.0.0',
            true
        );

        // Enqueue PWA styles
        wp_enqueue_style(
            'q-pwa-styles',
            Q_PLUGIN_URL . 'includes/css/pwa-styles.css',
            [],
            '1.0.0'
        );

        // Pass PWA settings to JavaScript
        wp_localize_script('q-pwa-manager', 'qPWASettings', [
            'enabled' => get_option('q_pwa_enabled', false),
            'offlineEnabled' => get_option('q_pwa_offline_enabled', true),
            'cacheStrategy' => get_option('q_pwa_cache_strategy', 'cache_first'),
            'manifestUrl' => home_url('/manifest.json'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('q_pwa_nonce')
        ]);
    }

    public static function add_pwa_meta_tags()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        $theme_color = get_option('q_pwa_theme_color', '#000000');
        $app_name = get_option('q_pwa_app_name', get_bloginfo('name'));

        echo '<meta name="theme-color" content="' . esc_attr($theme_color) . '">' . "\n";
        echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="default">' . "\n";
        echo '<meta name="apple-mobile-web-app-title" content="' . esc_attr($app_name) . '">' . "\n";
        echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";
        echo '<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">' . "\n";
    }

    public static function add_manifest_link()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        echo '<link rel="manifest" href="' . esc_url(home_url('/manifest.json')) . '">' . "\n";
    }

    // Check PWA requirements
    public static function check_pwa_requirements()
    {
        $requirements = [
            'https' => is_ssl(),
            'manifest' => get_option('q_pwa_enabled', false),
            'service_worker' => file_exists(ABSPATH . 'firebase-messaging-sw.js'),
            'icons' => !empty(get_option('q_pwa_icon_192', '')) && !empty(get_option('q_pwa_icon_512', ''))
        ];

        return $requirements;
    }

    // Get PWA status
    public static function get_pwa_status()
    {
        $requirements = self::check_pwa_requirements();
        $total = count($requirements);
        $met = count(array_filter($requirements));

        return [
            'requirements' => $requirements,
            'percentage' => round(($met / $total) * 100),
            'ready' => $met === $total
        ];
    }

    // Enqueue admin scripts and styles
    public static function enqueue_admin_scripts($hook)
    {
        // Debug: Log the hook name
        error_log('PWA Admin: Hook name: ' . $hook);

        // Only load on PWA settings page
        if ($hook !== 'settings_page_q-pwa-settings') {
            error_log('PWA Admin: Not on PWA settings page, skipping enqueue');
            return;
        }

        error_log('PWA Admin: Enqueuing admin scripts and styles');

        // Enqueue admin CSS
        wp_enqueue_style(
            'q-pwa-admin-styles',
            Q_PLUGIN_URL . 'includes/css/pwa-admin.css',
            [],
            '1.0.1'
        );

        // Enqueue admin JavaScript
        wp_enqueue_script(
            'q-pwa-admin-script',
            Q_PLUGIN_URL . 'includes/js/pwa-admin.js',
            ['jquery', 'wp-media'],
            '1.0.1',
            true
        );

        // Pass data to JavaScript
        wp_localize_script('q-pwa-admin-script', 'qPWAAdmin', [
            'manifestUrl' => Q_PWA_Manifest::get_manifest_url(),
            'siteUrl' => home_url('/'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('q_pwa_admin_nonce'),
            'isFirstTime' => !get_option('q_pwa_enabled', false)
        ]);

        error_log('PWA Admin: Scripts and styles enqueued successfully');
    }

    /**
     * Regenerate the offline page when PWA settings are updated
     */
    public static function regenerate_offline_page()
    {
        if (function_exists('q_generate_offline_page')) {
            q_generate_offline_page();
        }
    }
}
